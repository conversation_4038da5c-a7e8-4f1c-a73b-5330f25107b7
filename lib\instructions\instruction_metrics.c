// EVMC: Ethereum Client-VM Connector API.
// Copyright 2018 The EVMC Authors.
// Licensed under the Apache License, Version 2.0.

#include <evmc/instructions.h>

/**
 * Gas cost tiers, names from Yellow Paper.
 */
enum
{
    ZERO = 0,
    BASE = 2,
    VERYLOW = 3,
    LOW = 5,
    MID = 8,
    HIGH = 10,

    /**
     * Marks an instruction as undefined.
     *
     * The gas cost for undefined instructions is 0 because this is the cost of executing them
     * in practice in EVM implementations.
     */
    UNDEFINED = ZERO
};

enum
{
    /**
     * Defined in EIP-2929: Gas cost increases for state access opcodes.
     */
    WARM_STORAGE_READ_COST = 100
};

static struct evmc_instruction_metrics osaka_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*            SHL = 0x1b */ {VERYLOW, 2, -1},
    /*            SHR = 0x1c */ {VERYLOW, 2, -1},
    /*            SAR = 0x1d */ {VERYLOW, 2, -1},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {WARM_STORAGE_READ_COST, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {WARM_STORAGE_READ_COST, 4, -4},
    /* RETURNDATASIZE = 0x3d */ {BASE, 0, 1},
    /* RETURNDATACOPY = 0x3e */ {VERYLOW, 3, -3},
    /*    EXTCODEHASH = 0x3f */ {WARM_STORAGE_READ_COST, 1, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     PREVRANDAO = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*       CHAINID  = 0x46 */ {BASE, 0, 1},
    /*    SELFBALANCE = 0x47 */ {LOW, 0, 1},
    /*        BASEFEE = 0x48 */ {BASE, 0, 1},
    /*       BLOBHASH = 0x49 */ {BLOBHASH_GAS_COST, 0, 1},
    /*    BLOBBASEFEE = 0x4a */ {BLOBBASEFEE_GAS_COST, 0, 1},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*          TLOAD = 0x5c */ {TLOAD_GAS_COST, 1, 0},
    /*         TSTORE = 0x5d */ {TSTORE_GAS_COST, 2, -2},
    /*          MCOPY = 0x5e */ {MCOPY_GAS_COST, 3, -3},
    /*          PUSH0 = 0x5f */ {BASE, 0, 1},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*       CALLCODE = 0xf2 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*   DELEGATECALL = 0xf4 */ {WARM_STORAGE_READ_COST, 6, -5},
    /*        CREATE2 = 0xf5 */ {32000, 4, -3},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*     STATICCALL = 0xfa */ {WARM_STORAGE_READ_COST, 6, -5},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*         REVERT = 0xfd */ {ZERO, 2, -2},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {5000, 1, -1},
};

static struct evmc_instruction_metrics prague_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*            SHL = 0x1b */ {VERYLOW, 2, -1},
    /*            SHR = 0x1c */ {VERYLOW, 2, -1},
    /*            SAR = 0x1d */ {VERYLOW, 2, -1},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {WARM_STORAGE_READ_COST, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {WARM_STORAGE_READ_COST, 4, -4},
    /* RETURNDATASIZE = 0x3d */ {BASE, 0, 1},
    /* RETURNDATACOPY = 0x3e */ {VERYLOW, 3, -3},
    /*    EXTCODEHASH = 0x3f */ {WARM_STORAGE_READ_COST, 1, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     PREVRANDAO = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*       CHAINID  = 0x46 */ {BASE, 0, 1},
    /*    SELFBALANCE = 0x47 */ {LOW, 0, 1},
    /*        BASEFEE = 0x48 */ {BASE, 0, 1},
    /*                = 0x49 */ {UNDEFINED, 0, 0},
    /*                = 0x4a */ {UNDEFINED, 0, 0},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*                = 0x5c */ {UNDEFINED, 0, 0},
    /*                = 0x5d */ {UNDEFINED, 0, 0},
    /*                = 0x5e */ {UNDEFINED, 0, 0},
    /*          PUSH0 = 0x5f */ {BASE, 0, 1},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*       CALLCODE = 0xf2 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*   DELEGATECALL = 0xf4 */ {WARM_STORAGE_READ_COST, 6, -5},
    /*        CREATE2 = 0xf5 */ {32000, 4, -3},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*     STATICCALL = 0xfa */ {WARM_STORAGE_READ_COST, 6, -5},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*         REVERT = 0xfd */ {ZERO, 2, -2},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {5000, 1, -1},
};

static struct evmc_instruction_metrics cancun_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*            SHL = 0x1b */ {VERYLOW, 2, -1},
    /*            SHR = 0x1c */ {VERYLOW, 2, -1},
    /*            SAR = 0x1d */ {VERYLOW, 2, -1},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {WARM_STORAGE_READ_COST, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {WARM_STORAGE_READ_COST, 4, -4},
    /* RETURNDATASIZE = 0x3d */ {BASE, 0, 1},
    /* RETURNDATACOPY = 0x3e */ {VERYLOW, 3, -3},
    /*    EXTCODEHASH = 0x3f */ {WARM_STORAGE_READ_COST, 1, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     PREVRANDAO = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*       CHAINID  = 0x46 */ {BASE, 0, 1},
    /*    SELFBALANCE = 0x47 */ {LOW, 0, 1},
    /*        BASEFEE = 0x48 */ {BASE, 0, 1},
    /*                = 0x49 */ {UNDEFINED, 0, 0},
    /*                = 0x4a */ {UNDEFINED, 0, 0},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*                = 0x5c */ {UNDEFINED, 0, 0},
    /*                = 0x5d */ {UNDEFINED, 0, 0},
    /*                = 0x5e */ {UNDEFINED, 0, 0},
    /*          PUSH0 = 0x5f */ {BASE, 0, 1},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*       CALLCODE = 0xf2 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*   DELEGATECALL = 0xf4 */ {WARM_STORAGE_READ_COST, 6, -5},
    /*        CREATE2 = 0xf5 */ {32000, 4, -3},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*     STATICCALL = 0xfa */ {WARM_STORAGE_READ_COST, 6, -5},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*         REVERT = 0xfd */ {ZERO, 2, -2},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {5000, 1, -1},
};

static struct evmc_instruction_metrics shanghai_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*            SHL = 0x1b */ {VERYLOW, 2, -1},
    /*            SHR = 0x1c */ {VERYLOW, 2, -1},
    /*            SAR = 0x1d */ {VERYLOW, 2, -1},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {WARM_STORAGE_READ_COST, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {WARM_STORAGE_READ_COST, 4, -4},
    /* RETURNDATASIZE = 0x3d */ {BASE, 0, 1},
    /* RETURNDATACOPY = 0x3e */ {VERYLOW, 3, -3},
    /*    EXTCODEHASH = 0x3f */ {WARM_STORAGE_READ_COST, 1, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     PREVRANDAO = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*       CHAINID  = 0x46 */ {BASE, 0, 1},
    /*    SELFBALANCE = 0x47 */ {LOW, 0, 1},
    /*        BASEFEE = 0x48 */ {BASE, 0, 1},
    /*                = 0x49 */ {UNDEFINED, 0, 0},
    /*                = 0x4a */ {UNDEFINED, 0, 0},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*                = 0x5c */ {UNDEFINED, 0, 0},
    /*                = 0x5d */ {UNDEFINED, 0, 0},
    /*                = 0x5e */ {UNDEFINED, 0, 0},
    /*          PUSH0 = 0x5f */ {BASE, 0, 1},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*       CALLCODE = 0xf2 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*   DELEGATECALL = 0xf4 */ {WARM_STORAGE_READ_COST, 6, -5},
    /*        CREATE2 = 0xf5 */ {32000, 4, -3},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*     STATICCALL = 0xfa */ {WARM_STORAGE_READ_COST, 6, -5},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*         REVERT = 0xfd */ {ZERO, 2, -2},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {5000, 1, -1},
};

static struct evmc_instruction_metrics london_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*            SHL = 0x1b */ {VERYLOW, 2, -1},
    /*            SHR = 0x1c */ {VERYLOW, 2, -1},
    /*            SAR = 0x1d */ {VERYLOW, 2, -1},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {WARM_STORAGE_READ_COST, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {WARM_STORAGE_READ_COST, 4, -4},
    /* RETURNDATASIZE = 0x3d */ {BASE, 0, 1},
    /* RETURNDATACOPY = 0x3e */ {VERYLOW, 3, -3},
    /*    EXTCODEHASH = 0x3f */ {WARM_STORAGE_READ_COST, 1, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     DIFFICULTY = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*       CHAINID  = 0x46 */ {BASE, 0, 1},
    /*    SELFBALANCE = 0x47 */ {LOW, 0, 1},
    /*        BASEFEE = 0x48 */ {BASE, 0, 1},
    /*                = 0x49 */ {UNDEFINED, 0, 0},
    /*                = 0x4a */ {UNDEFINED, 0, 0},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*                = 0x5c */ {UNDEFINED, 0, 0},
    /*                = 0x5d */ {UNDEFINED, 0, 0},
    /*                = 0x5e */ {UNDEFINED, 0, 0},
    /*                = 0x5f */ {UNDEFINED, 0, 0},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*       CALLCODE = 0xf2 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*   DELEGATECALL = 0xf4 */ {WARM_STORAGE_READ_COST, 6, -5},
    /*        CREATE2 = 0xf5 */ {32000, 4, -3},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*     STATICCALL = 0xfa */ {WARM_STORAGE_READ_COST, 6, -5},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*         REVERT = 0xfd */ {ZERO, 2, -2},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {5000, 1, -1},
};

static struct evmc_instruction_metrics berlin_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*            SHL = 0x1b */ {VERYLOW, 2, -1},
    /*            SHR = 0x1c */ {VERYLOW, 2, -1},
    /*            SAR = 0x1d */ {VERYLOW, 2, -1},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {WARM_STORAGE_READ_COST, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {WARM_STORAGE_READ_COST, 4, -4},
    /* RETURNDATASIZE = 0x3d */ {BASE, 0, 1},
    /* RETURNDATACOPY = 0x3e */ {VERYLOW, 3, -3},
    /*    EXTCODEHASH = 0x3f */ {WARM_STORAGE_READ_COST, 1, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     DIFFICULTY = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*       CHAINID  = 0x46 */ {BASE, 0, 1},
    /*    SELFBALANCE = 0x47 */ {LOW, 0, 1},
    /*                = 0x48 */ {UNDEFINED, 0, 0},
    /*                = 0x49 */ {UNDEFINED, 0, 0},
    /*                = 0x4a */ {UNDEFINED, 0, 0},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {WARM_STORAGE_READ_COST, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*                = 0x5c */ {UNDEFINED, 0, 0},
    /*                = 0x5d */ {UNDEFINED, 0, 0},
    /*                = 0x5e */ {UNDEFINED, 0, 0},
    /*                = 0x5f */ {UNDEFINED, 0, 0},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*       CALLCODE = 0xf2 */ {WARM_STORAGE_READ_COST, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*   DELEGATECALL = 0xf4 */ {WARM_STORAGE_READ_COST, 6, -5},
    /*        CREATE2 = 0xf5 */ {32000, 4, -3},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*     STATICCALL = 0xfa */ {WARM_STORAGE_READ_COST, 6, -5},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*         REVERT = 0xfd */ {ZERO, 2, -2},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {5000, 1, -1},
};

static struct evmc_instruction_metrics istanbul_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*            SHL = 0x1b */ {VERYLOW, 2, -1},
    /*            SHR = 0x1c */ {VERYLOW, 2, -1},
    /*            SAR = 0x1d */ {VERYLOW, 2, -1},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {700, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {700, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {700, 4, -4},
    /* RETURNDATASIZE = 0x3d */ {BASE, 0, 1},
    /* RETURNDATACOPY = 0x3e */ {VERYLOW, 3, -3},
    /*    EXTCODEHASH = 0x3f */ {700, 1, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     DIFFICULTY = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*       CHAINID  = 0x46 */ {BASE, 0, 1},
    /*    SELFBALANCE = 0x47 */ {LOW, 0, 1},
    /*                = 0x48 */ {UNDEFINED, 0, 0},
    /*                = 0x49 */ {UNDEFINED, 0, 0},
    /*                = 0x4a */ {UNDEFINED, 0, 0},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {800, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*                = 0x5c */ {UNDEFINED, 0, 0},
    /*                = 0x5d */ {UNDEFINED, 0, 0},
    /*                = 0x5e */ {UNDEFINED, 0, 0},
    /*                = 0x5f */ {UNDEFINED, 0, 0},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {700, 7, -6},
    /*       CALLCODE = 0xf2 */ {700, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*   DELEGATECALL = 0xf4 */ {700, 6, -5},
    /*        CREATE2 = 0xf5 */ {32000, 4, -3},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*     STATICCALL = 0xfa */ {700, 6, -5},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*         REVERT = 0xfd */ {ZERO, 2, -2},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {5000, 1, -1},
};

static struct evmc_instruction_metrics constantinople_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*            SHL = 0x1b */ {VERYLOW, 2, -1},
    /*            SHR = 0x1c */ {VERYLOW, 2, -1},
    /*            SAR = 0x1d */ {VERYLOW, 2, -1},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {400, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {700, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {700, 4, -4},
    /* RETURNDATASIZE = 0x3d */ {BASE, 0, 1},
    /* RETURNDATACOPY = 0x3e */ {VERYLOW, 3, -3},
    /*    EXTCODEHASH = 0x3f */ {400, 1, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     DIFFICULTY = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*                = 0x46 */ {UNDEFINED, 0, 0},
    /*                = 0x47 */ {UNDEFINED, 0, 0},
    /*                = 0x48 */ {UNDEFINED, 0, 0},
    /*                = 0x49 */ {UNDEFINED, 0, 0},
    /*                = 0x4a */ {UNDEFINED, 0, 0},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {200, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*                = 0x5c */ {UNDEFINED, 0, 0},
    /*                = 0x5d */ {UNDEFINED, 0, 0},
    /*                = 0x5e */ {UNDEFINED, 0, 0},
    /*                = 0x5f */ {UNDEFINED, 0, 0},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {700, 7, -6},
    /*       CALLCODE = 0xf2 */ {700, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*   DELEGATECALL = 0xf4 */ {700, 6, -5},
    /*        CREATE2 = 0xf5 */ {32000, 4, -3},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*     STATICCALL = 0xfa */ {700, 6, -5},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*         REVERT = 0xfd */ {ZERO, 2, -2},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {5000, 1, -1},
};

static struct evmc_instruction_metrics byzantium_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*                = 0x1b */ {UNDEFINED, 0, 0},
    /*                = 0x1c */ {UNDEFINED, 0, 0},
    /*                = 0x1d */ {UNDEFINED, 0, 0},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {400, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {700, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {700, 4, -4},
    /* RETURNDATASIZE = 0x3d */ {BASE, 0, 1},
    /* RETURNDATACOPY = 0x3e */ {VERYLOW, 3, -3},
    /*                = 0x3f */ {UNDEFINED, 0, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     DIFFICULTY = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*                = 0x46 */ {UNDEFINED, 0, 0},
    /*                = 0x47 */ {UNDEFINED, 0, 0},
    /*                = 0x48 */ {UNDEFINED, 0, 0},
    /*                = 0x49 */ {UNDEFINED, 0, 0},
    /*                = 0x4a */ {UNDEFINED, 0, 0},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {200, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*                = 0x5c */ {UNDEFINED, 0, 0},
    /*                = 0x5d */ {UNDEFINED, 0, 0},
    /*                = 0x5e */ {UNDEFINED, 0, 0},
    /*                = 0x5f */ {UNDEFINED, 0, 0},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {700, 7, -6},
    /*       CALLCODE = 0xf2 */ {700, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*   DELEGATECALL = 0xf4 */ {700, 6, -5},
    /*                = 0xf5 */ {UNDEFINED, 0, 0},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*     STATICCALL = 0xfa */ {700, 6, -5},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*         REVERT = 0xfd */ {ZERO, 2, -2},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {5000, 1, -1},
};

static struct evmc_instruction_metrics tangerine_whistle_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*                = 0x1b */ {UNDEFINED, 0, 0},
    /*                = 0x1c */ {UNDEFINED, 0, 0},
    /*                = 0x1d */ {UNDEFINED, 0, 0},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {400, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {700, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {700, 4, -4},
    /*                = 0x3d */ {UNDEFINED, 0, 0},
    /*                = 0x3e */ {UNDEFINED, 0, 0},
    /*                = 0x3f */ {UNDEFINED, 0, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     DIFFICULTY = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*                = 0x46 */ {UNDEFINED, 0, 0},
    /*                = 0x47 */ {UNDEFINED, 0, 0},
    /*                = 0x48 */ {UNDEFINED, 0, 0},
    /*                = 0x49 */ {UNDEFINED, 0, 0},
    /*                = 0x4a */ {UNDEFINED, 0, 0},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {200, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*                = 0x5c */ {UNDEFINED, 0, 0},
    /*                = 0x5d */ {UNDEFINED, 0, 0},
    /*                = 0x5e */ {UNDEFINED, 0, 0},
    /*                = 0x5f */ {UNDEFINED, 0, 0},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {700, 7, -6},
    /*       CALLCODE = 0xf2 */ {700, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*   DELEGATECALL = 0xf4 */ {700, 6, -5},
    /*                = 0xf5 */ {UNDEFINED, 0, 0},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*                = 0xfa */ {UNDEFINED, 0, 0},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*                = 0xfd */ {UNDEFINED, 0, 0},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {5000, 1, -1},
};

static struct evmc_instruction_metrics homestead_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*                = 0x1b */ {UNDEFINED, 0, 0},
    /*                = 0x1c */ {UNDEFINED, 0, 0},
    /*                = 0x1d */ {UNDEFINED, 0, 0},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {20, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {20, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {20, 4, -4},
    /*                = 0x3d */ {UNDEFINED, 0, 0},
    /*                = 0x3e */ {UNDEFINED, 0, 0},
    /*                = 0x3f */ {UNDEFINED, 0, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     DIFFICULTY = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*                = 0x46 */ {UNDEFINED, 0, 0},
    /*                = 0x47 */ {UNDEFINED, 0, 0},
    /*                = 0x48 */ {UNDEFINED, 0, 0},
    /*                = 0x49 */ {UNDEFINED, 0, 0},
    /*                = 0x4a */ {UNDEFINED, 0, 0},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {50, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*                = 0x5c */ {UNDEFINED, 0, 0},
    /*                = 0x5d */ {UNDEFINED, 0, 0},
    /*                = 0x5e */ {UNDEFINED, 0, 0},
    /*                = 0x5f */ {UNDEFINED, 0, 0},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {40, 7, -6},
    /*       CALLCODE = 0xf2 */ {40, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*   DELEGATECALL = 0xf4 */ {40, 6, -5},
    /*                = 0xf5 */ {UNDEFINED, 0, 0},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*                = 0xfa */ {UNDEFINED, 0, 0},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*                = 0xfd */ {UNDEFINED, 0, 0},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {ZERO, 1, -1},
};

static struct evmc_instruction_metrics frontier_metrics[256] = {
    /*           STOP = 0x00 */ {ZERO, 0, 0},
    /*            ADD = 0x01 */ {VERYLOW, 2, -1},
    /*            MUL = 0x02 */ {LOW, 2, -1},
    /*            SUB = 0x03 */ {VERYLOW, 2, -1},
    /*            DIV = 0x04 */ {LOW, 2, -1},
    /*           SDIV = 0x05 */ {LOW, 2, -1},
    /*            MOD = 0x06 */ {LOW, 2, -1},
    /*           SMOD = 0x07 */ {LOW, 2, -1},
    /*         ADDMOD = 0x08 */ {MID, 3, -2},
    /*         MULMOD = 0x09 */ {MID, 3, -2},
    /*            EXP = 0x0a */ {HIGH, 2, -1},
    /*     SIGNEXTEND = 0x0b */ {LOW, 2, -1},
    /*                = 0x0c */ {UNDEFINED, 0, 0},
    /*                = 0x0d */ {UNDEFINED, 0, 0},
    /*                = 0x0e */ {UNDEFINED, 0, 0},
    /*                = 0x0f */ {UNDEFINED, 0, 0},
    /*             LT = 0x10 */ {VERYLOW, 2, -1},
    /*             GT = 0x11 */ {VERYLOW, 2, -1},
    /*            SLT = 0x12 */ {VERYLOW, 2, -1},
    /*            SGT = 0x13 */ {VERYLOW, 2, -1},
    /*             EQ = 0x14 */ {VERYLOW, 2, -1},
    /*         ISZERO = 0x15 */ {VERYLOW, 1, 0},
    /*            AND = 0x16 */ {VERYLOW, 2, -1},
    /*             OR = 0x17 */ {VERYLOW, 2, -1},
    /*            XOR = 0x18 */ {VERYLOW, 2, -1},
    /*            NOT = 0x19 */ {VERYLOW, 1, 0},
    /*           BYTE = 0x1a */ {VERYLOW, 2, -1},
    /*                = 0x1b */ {UNDEFINED, 0, 0},
    /*                = 0x1c */ {UNDEFINED, 0, 0},
    /*                = 0x1d */ {UNDEFINED, 0, 0},
    /*                = 0x1e */ {UNDEFINED, 0, 0},
    /*                = 0x1f */ {UNDEFINED, 0, 0},
    /*      KECCAK256 = 0x20 */ {30, 2, -1},
    /*                = 0x21 */ {UNDEFINED, 0, 0},
    /*                = 0x22 */ {UNDEFINED, 0, 0},
    /*                = 0x23 */ {UNDEFINED, 0, 0},
    /*                = 0x24 */ {UNDEFINED, 0, 0},
    /*                = 0x25 */ {UNDEFINED, 0, 0},
    /*                = 0x26 */ {UNDEFINED, 0, 0},
    /*                = 0x27 */ {UNDEFINED, 0, 0},
    /*                = 0x28 */ {UNDEFINED, 0, 0},
    /*                = 0x29 */ {UNDEFINED, 0, 0},
    /*                = 0x2a */ {UNDEFINED, 0, 0},
    /*                = 0x2b */ {UNDEFINED, 0, 0},
    /*                = 0x2c */ {UNDEFINED, 0, 0},
    /*                = 0x2d */ {UNDEFINED, 0, 0},
    /*                = 0x2e */ {UNDEFINED, 0, 0},
    /*                = 0x2f */ {UNDEFINED, 0, 0},
    /*        ADDRESS = 0x30 */ {BASE, 0, 1},
    /*        BALANCE = 0x31 */ {20, 1, 0},
    /*         ORIGIN = 0x32 */ {BASE, 0, 1},
    /*         CALLER = 0x33 */ {BASE, 0, 1},
    /*      CALLVALUE = 0x34 */ {BASE, 0, 1},
    /*   CALLDATALOAD = 0x35 */ {VERYLOW, 1, 0},
    /*   CALLDATASIZE = 0x36 */ {BASE, 0, 1},
    /*   CALLDATACOPY = 0x37 */ {VERYLOW, 3, -3},
    /*       CODESIZE = 0x38 */ {BASE, 0, 1},
    /*       CODECOPY = 0x39 */ {VERYLOW, 3, -3},
    /*       GASPRICE = 0x3a */ {BASE, 0, 1},
    /*    EXTCODESIZE = 0x3b */ {20, 1, 0},
    /*    EXTCODECOPY = 0x3c */ {20, 4, -4},
    /*                = 0x3d */ {UNDEFINED, 0, 0},
    /*                = 0x3e */ {UNDEFINED, 0, 0},
    /*                = 0x3f */ {UNDEFINED, 0, 0},
    /*      BLOCKHASH = 0x40 */ {20, 1, 0},
    /*       COINBASE = 0x41 */ {BASE, 0, 1},
    /*      TIMESTAMP = 0x42 */ {BASE, 0, 1},
    /*         NUMBER = 0x43 */ {BASE, 0, 1},
    /*     DIFFICULTY = 0x44 */ {BASE, 0, 1},
    /*       GASLIMIT = 0x45 */ {BASE, 0, 1},
    /*                = 0x46 */ {UNDEFINED, 0, 0},
    /*                = 0x47 */ {UNDEFINED, 0, 0},
    /*                = 0x48 */ {UNDEFINED, 0, 0},
    /*                = 0x49 */ {UNDEFINED, 0, 0},
    /*                = 0x4a */ {UNDEFINED, 0, 0},
    /*                = 0x4b */ {UNDEFINED, 0, 0},
    /*                = 0x4c */ {UNDEFINED, 0, 0},
    /*                = 0x4d */ {UNDEFINED, 0, 0},
    /*                = 0x4e */ {UNDEFINED, 0, 0},
    /*                = 0x4f */ {UNDEFINED, 0, 0},
    /*            POP = 0x50 */ {BASE, 1, -1},
    /*          MLOAD = 0x51 */ {VERYLOW, 1, 0},
    /*         MSTORE = 0x52 */ {VERYLOW, 2, -2},
    /*        MSTORE8 = 0x53 */ {VERYLOW, 2, -2},
    /*          SLOAD = 0x54 */ {50, 1, 0},
    /*         SSTORE = 0x55 */ {0, 2, -2},
    /*           JUMP = 0x56 */ {MID, 1, -1},
    /*          JUMPI = 0x57 */ {HIGH, 2, -2},
    /*             PC = 0x58 */ {BASE, 0, 1},
    /*          MSIZE = 0x59 */ {BASE, 0, 1},
    /*            GAS = 0x5a */ {BASE, 0, 1},
    /*       JUMPDEST = 0x5b */ {1, 0, 0},
    /*                = 0x5c */ {UNDEFINED, 0, 0},
    /*                = 0x5d */ {UNDEFINED, 0, 0},
    /*                = 0x5e */ {UNDEFINED, 0, 0},
    /*                = 0x5f */ {UNDEFINED, 0, 0},
    /*          PUSH1 = 0x60 */ {VERYLOW, 0, 1},
    /*          PUSH2 = 0x61 */ {VERYLOW, 0, 1},
    /*          PUSH3 = 0x62 */ {VERYLOW, 0, 1},
    /*          PUSH4 = 0x63 */ {VERYLOW, 0, 1},
    /*          PUSH5 = 0x64 */ {VERYLOW, 0, 1},
    /*          PUSH6 = 0x65 */ {VERYLOW, 0, 1},
    /*          PUSH7 = 0x66 */ {VERYLOW, 0, 1},
    /*          PUSH8 = 0x67 */ {VERYLOW, 0, 1},
    /*          PUSH9 = 0x68 */ {VERYLOW, 0, 1},
    /*         PUSH10 = 0x69 */ {VERYLOW, 0, 1},
    /*         PUSH11 = 0x6a */ {VERYLOW, 0, 1},
    /*         PUSH12 = 0x6b */ {VERYLOW, 0, 1},
    /*         PUSH13 = 0x6c */ {VERYLOW, 0, 1},
    /*         PUSH14 = 0x6d */ {VERYLOW, 0, 1},
    /*         PUSH15 = 0x6e */ {VERYLOW, 0, 1},
    /*         PUSH16 = 0x6f */ {VERYLOW, 0, 1},
    /*         PUSH17 = 0x70 */ {VERYLOW, 0, 1},
    /*         PUSH18 = 0x71 */ {VERYLOW, 0, 1},
    /*         PUSH19 = 0x72 */ {VERYLOW, 0, 1},
    /*         PUSH20 = 0x73 */ {VERYLOW, 0, 1},
    /*         PUSH21 = 0x74 */ {VERYLOW, 0, 1},
    /*         PUSH22 = 0x75 */ {VERYLOW, 0, 1},
    /*         PUSH23 = 0x76 */ {VERYLOW, 0, 1},
    /*         PUSH24 = 0x77 */ {VERYLOW, 0, 1},
    /*         PUSH25 = 0x78 */ {VERYLOW, 0, 1},
    /*         PUSH26 = 0x79 */ {VERYLOW, 0, 1},
    /*         PUSH27 = 0x7a */ {VERYLOW, 0, 1},
    /*         PUSH28 = 0x7b */ {VERYLOW, 0, 1},
    /*         PUSH29 = 0x7c */ {VERYLOW, 0, 1},
    /*         PUSH30 = 0x7d */ {VERYLOW, 0, 1},
    /*         PUSH31 = 0x7e */ {VERYLOW, 0, 1},
    /*         PUSH32 = 0x7f */ {VERYLOW, 0, 1},
    /*           DUP1 = 0x80 */ {VERYLOW, 1, 1},
    /*           DUP2 = 0x81 */ {VERYLOW, 2, 1},
    /*           DUP3 = 0x82 */ {VERYLOW, 3, 1},
    /*           DUP4 = 0x83 */ {VERYLOW, 4, 1},
    /*           DUP5 = 0x84 */ {VERYLOW, 5, 1},
    /*           DUP6 = 0x85 */ {VERYLOW, 6, 1},
    /*           DUP7 = 0x86 */ {VERYLOW, 7, 1},
    /*           DUP8 = 0x87 */ {VERYLOW, 8, 1},
    /*           DUP9 = 0x88 */ {VERYLOW, 9, 1},
    /*          DUP10 = 0x89 */ {VERYLOW, 10, 1},
    /*          DUP11 = 0x8a */ {VERYLOW, 11, 1},
    /*          DUP12 = 0x8b */ {VERYLOW, 12, 1},
    /*          DUP13 = 0x8c */ {VERYLOW, 13, 1},
    /*          DUP14 = 0x8d */ {VERYLOW, 14, 1},
    /*          DUP15 = 0x8e */ {VERYLOW, 15, 1},
    /*          DUP16 = 0x8f */ {VERYLOW, 16, 1},
    /*          SWAP1 = 0x90 */ {VERYLOW, 2, 0},
    /*          SWAP2 = 0x91 */ {VERYLOW, 3, 0},
    /*          SWAP3 = 0x92 */ {VERYLOW, 4, 0},
    /*          SWAP4 = 0x93 */ {VERYLOW, 5, 0},
    /*          SWAP5 = 0x94 */ {VERYLOW, 6, 0},
    /*          SWAP6 = 0x95 */ {VERYLOW, 7, 0},
    /*          SWAP7 = 0x96 */ {VERYLOW, 8, 0},
    /*          SWAP8 = 0x97 */ {VERYLOW, 9, 0},
    /*          SWAP9 = 0x98 */ {VERYLOW, 10, 0},
    /*         SWAP10 = 0x99 */ {VERYLOW, 11, 0},
    /*         SWAP11 = 0x9a */ {VERYLOW, 12, 0},
    /*         SWAP12 = 0x9b */ {VERYLOW, 13, 0},
    /*         SWAP13 = 0x9c */ {VERYLOW, 14, 0},
    /*         SWAP14 = 0x9d */ {VERYLOW, 15, 0},
    /*         SWAP15 = 0x9e */ {VERYLOW, 16, 0},
    /*         SWAP16 = 0x9f */ {VERYLOW, 17, 0},
    /*           LOG0 = 0xa0 */ {1 * 375, 2, -2},
    /*           LOG1 = 0xa1 */ {2 * 375, 3, -3},
    /*           LOG2 = 0xa2 */ {3 * 375, 4, -4},
    /*           LOG3 = 0xa3 */ {4 * 375, 5, -5},
    /*           LOG4 = 0xa4 */ {5 * 375, 6, -6},
    /*                = 0xa5 */ {UNDEFINED, 0, 0},
    /*                = 0xa6 */ {UNDEFINED, 0, 0},
    /*                = 0xa7 */ {UNDEFINED, 0, 0},
    /*                = 0xa8 */ {UNDEFINED, 0, 0},
    /*                = 0xa9 */ {UNDEFINED, 0, 0},
    /*                = 0xaa */ {UNDEFINED, 0, 0},
    /*                = 0xab */ {UNDEFINED, 0, 0},
    /*                = 0xac */ {UNDEFINED, 0, 0},
    /*                = 0xad */ {UNDEFINED, 0, 0},
    /*                = 0xae */ {UNDEFINED, 0, 0},
    /*                = 0xaf */ {UNDEFINED, 0, 0},
    /*                = 0xb0 */ {UNDEFINED, 0, 0},
    /*                = 0xb1 */ {UNDEFINED, 0, 0},
    /*                = 0xb2 */ {UNDEFINED, 0, 0},
    /*                = 0xb3 */ {UNDEFINED, 0, 0},
    /*                = 0xb4 */ {UNDEFINED, 0, 0},
    /*                = 0xb5 */ {UNDEFINED, 0, 0},
    /*                = 0xb6 */ {UNDEFINED, 0, 0},
    /*                = 0xb7 */ {UNDEFINED, 0, 0},
    /*                = 0xb8 */ {UNDEFINED, 0, 0},
    /*                = 0xb9 */ {UNDEFINED, 0, 0},
    /*                = 0xba */ {UNDEFINED, 0, 0},
    /*                = 0xbb */ {UNDEFINED, 0, 0},
    /*                = 0xbc */ {UNDEFINED, 0, 0},
    /*                = 0xbd */ {UNDEFINED, 0, 0},
    /*                = 0xbe */ {UNDEFINED, 0, 0},
    /*                = 0xbf */ {UNDEFINED, 0, 0},
    /*                = 0xc0 */ {UNDEFINED, 0, 0},
    /*                = 0xc1 */ {UNDEFINED, 0, 0},
    /*                = 0xc2 */ {UNDEFINED, 0, 0},
    /*                = 0xc3 */ {UNDEFINED, 0, 0},
    /*                = 0xc4 */ {UNDEFINED, 0, 0},
    /*                = 0xc5 */ {UNDEFINED, 0, 0},
    /*                = 0xc6 */ {UNDEFINED, 0, 0},
    /*                = 0xc7 */ {UNDEFINED, 0, 0},
    /*                = 0xc8 */ {UNDEFINED, 0, 0},
    /*                = 0xc9 */ {UNDEFINED, 0, 0},
    /*                = 0xca */ {UNDEFINED, 0, 0},
    /*                = 0xcb */ {UNDEFINED, 0, 0},
    /*                = 0xcc */ {UNDEFINED, 0, 0},
    /*                = 0xcd */ {UNDEFINED, 0, 0},
    /*                = 0xce */ {UNDEFINED, 0, 0},
    /*                = 0xcf */ {UNDEFINED, 0, 0},
    /*                = 0xd0 */ {UNDEFINED, 0, 0},
    /*                = 0xd1 */ {UNDEFINED, 0, 0},
    /*                = 0xd2 */ {UNDEFINED, 0, 0},
    /*                = 0xd3 */ {UNDEFINED, 0, 0},
    /*                = 0xd4 */ {UNDEFINED, 0, 0},
    /*                = 0xd5 */ {UNDEFINED, 0, 0},
    /*                = 0xd6 */ {UNDEFINED, 0, 0},
    /*                = 0xd7 */ {UNDEFINED, 0, 0},
    /*                = 0xd8 */ {UNDEFINED, 0, 0},
    /*                = 0xd9 */ {UNDEFINED, 0, 0},
    /*                = 0xda */ {UNDEFINED, 0, 0},
    /*                = 0xdb */ {UNDEFINED, 0, 0},
    /*                = 0xdc */ {UNDEFINED, 0, 0},
    /*                = 0xdd */ {UNDEFINED, 0, 0},
    /*                = 0xde */ {UNDEFINED, 0, 0},
    /*                = 0xdf */ {UNDEFINED, 0, 0},
    /*                = 0xe0 */ {UNDEFINED, 0, 0},
    /*                = 0xe1 */ {UNDEFINED, 0, 0},
    /*                = 0xe2 */ {UNDEFINED, 0, 0},
    /*                = 0xe3 */ {UNDEFINED, 0, 0},
    /*                = 0xe4 */ {UNDEFINED, 0, 0},
    /*                = 0xe5 */ {UNDEFINED, 0, 0},
    /*                = 0xe6 */ {UNDEFINED, 0, 0},
    /*                = 0xe7 */ {UNDEFINED, 0, 0},
    /*                = 0xe8 */ {UNDEFINED, 0, 0},
    /*                = 0xe9 */ {UNDEFINED, 0, 0},
    /*                = 0xea */ {UNDEFINED, 0, 0},
    /*                = 0xeb */ {UNDEFINED, 0, 0},
    /*                = 0xec */ {UNDEFINED, 0, 0},
    /*                = 0xed */ {UNDEFINED, 0, 0},
    /*                = 0xee */ {UNDEFINED, 0, 0},
    /*                = 0xef */ {UNDEFINED, 0, 0},
    /*         CREATE = 0xf0 */ {32000, 3, -2},
    /*           CALL = 0xf1 */ {40, 7, -6},
    /*       CALLCODE = 0xf2 */ {40, 7, -6},
    /*         RETURN = 0xf3 */ {ZERO, 2, -2},
    /*                = 0xf4 */ {UNDEFINED, 0, 0},
    /*                = 0xf5 */ {UNDEFINED, 0, 0},
    /*                = 0xf6 */ {UNDEFINED, 0, 0},
    /*                = 0xf7 */ {UNDEFINED, 0, 0},
    /*                = 0xf8 */ {UNDEFINED, 0, 0},
    /*                = 0xf9 */ {UNDEFINED, 0, 0},
    /*                = 0xfa */ {UNDEFINED, 0, 0},
    /*                = 0xfb */ {UNDEFINED, 0, 0},
    /*                = 0xfc */ {UNDEFINED, 0, 0},
    /*                = 0xfd */ {UNDEFINED, 0, 0},
    /*        INVALID = 0xfe */ {ZERO, 0, 0},
    /*   SELFDESTRUCT = 0xff */ {ZERO, 1, -1},
};

const struct evmc_instruction_metrics* evmc_get_instruction_metrics_table(
    enum evmc_revision revision)
{
    switch (revision)
    {
    case EVMC_EXPERIMENTAL:
    case EVMC_OSAKA:
        return osaka_metrics;
    case EVMC_PRAGUE:
        return prague_metrics;
    case EVMC_CANCUN:
        return cancun_metrics;
    case EVMC_SHANGHAI:
        return shanghai_metrics;
    case EVMC_PARIS:
    case EVMC_LONDON:
        return london_metrics;
    case EVMC_BERLIN:
        return berlin_metrics;
    case EVMC_ISTANBUL:
        return istanbul_metrics;
    case EVMC_PETERSBURG:
    case EVMC_CONSTANTINOPLE:
        return constantinople_metrics;
    case EVMC_BYZANTIUM:
        return byzantium_metrics;
    case EVMC_SPURIOUS_DRAGON:
    case EVMC_TANGERINE_WHISTLE:
        return tangerine_whistle_metrics;
    case EVMC_HOMESTEAD:
        return homestead_metrics;
    case EVMC_FRONTIER:
        return frontier_metrics;
    default:
        return NULL;
    }
}
